I need to refactor handling of files in @/Users/<USER>/projects/poll-vue-app/src/entities/question/controllers/VariantsController.js and in @/Users/<USER>/projects/poll-vue-app/src/entities/question/ui/Variants.vue 

Recently we added file and screenshot upload support for this controller. Work is WIP but I wanna do the following

Implement some new class for handling attached images/files VariantsController. (suggest names)

it should: 

add files (can be in loading state), upload files (endpoint and api call will be added later), requesting screenshots (sending message to parent window), adding screenshot (can be loading), uploading screenshots

important note: sometimes we can receive already uploaded images/screenshots from server so we should make images addable from variants controller.

if user uploads it it's always select image (or screenshot area) -> (add loading state) -> upload (endpoint and api will be later)

also tell me how can we identify images when attached  