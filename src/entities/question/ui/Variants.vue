<script setup>
import SelfVariantField from '@entities/question/ui/SelfVariantField.vue'
import { usePollStore } from '@entities/poll/model/store'
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import Check from '@shared/ui/Check.vue'
import CheckGroup from '@shared/ui/CheckGroup.vue'
import FileCheck from '@shared/ui/FileCheck.vue'
import FilePreview from '@shared/ui/FilePreview.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import Select from '@shared/ui/Select/FcSelect.vue'
import Textarea from '@shared/ui/Textarea.vue'
import VariantsFileAttachment from '@shared/ui/VariantsFileAttachment.vue'
import { useMediaQuery } from '@vueuse/core'
import { computed, isRef, toValue } from 'vue'
import VariantsGallery from './VariantsGallery.vue'

const props = defineProps({
  /**
   * @type {import('@/src/entities/question/controllers/VariantsController').VariantsController}
   */
  variantsController: {
    type: Object,
    required: true,
  },

  /**
   * @description Идентификатор группы файлов. Используется для группировки файлов в Fancybox.
   */
  groupId: {
    type: String,
    required: false,
    default: () => 'global',
  },
  customClass: {
    type: String,
    default: '',
  },
  /**
   * @description Тип отображения медиафайлов
   * @type {'default' | 'gallery'}
   */
  mediaView: {
    type: String,
    default: 'default',
    validator: value => ['default', 'gallery'].includes(value),
  },
  tabletView: {
    type: Boolean,
    default: false,
  },
  hasComment: {
    type: Boolean,
    required: false,
  },
})

const emit = defineEmits(['change'])

const isMobile = useMediaQuery('(max-width: 679px)')
const simplifiedStore = useSimplifiedStore()
const pollStore = usePollStore()

const {
  label,
  showTooltips,
  skipped,
  isMultiple,
  isSelfVariantChecked,
  shouldShowSelfVariantComment,
  variantsType,
  variants,
  variantsWithFiles,
  dropdown,
  answers,
  textFieldValue,
  placeholderText,
  selfVariantMinLength,
  selfVariantMaxLength,
  error,
  questionScreenshot,
  screenshots,
  files,
  screenshotError,
  errorKind,
  showScreenshotButton,
  showUploadButton,
} = props.variantsController

const selfVariantChecker = v => v.value === 'is_self_answer' || v.isRecipientSelfAnswer

const computedVariants = computed(() => {
  const allVariants = variants.value

  return allVariants.map(variant => ({
    label: toValue(variant.text),
    value: variant.id,
    disabled: toValue(variant.disabled),
    inactive: toValue(variant.inactive),
    url: toValue(variant.url),
    poster: toValue(variant.poster),
    description: toValue(showTooltips) ? toValue(variant.description) : null,
    isRecipientSelfAnswer: variant.isRecipientSelfAnswer,
  }))
})

const inputType = computed(() => isMultiple.value ? 'checkbox' : 'radio')

function onChange(event) {
  emit('change', event)

  props.variantsController.skipped = false
}

const isSingleOrMultipleType = computed(() => {
  return variantsType.value === 0 || variantsType.value === 1
})

const isDropdown = computed(() => props.tabletView ? false : dropdown.value)

const dropdownOptions = computed(() => props.variantsController.getDropdownOptions())
const isSkipped = computed(() => skipped.value)
const isInvalid = computed(() => error.value)

function handleDropdownChange(selectedOptions) {
  props.variantsController.handleDropdownChange(selectedOptions)
  onChange(selectedOptions)
}

const questionVariantsClasses = computed(() => {
  return {
    'question-variants': true,
    'question-variants--dropdown': isDropdown.value,
    'question-variants--invalid': isInvalid.value,
    'question-variants--skipped': isSkipped.value,
    [props.customClass]: true,
  }
})

function onDropdownOpen(open) {
  if (open) {
    if (isRef(props.variantsController.skipped)) {
      props.variantsController.skipped.value = false
    }
    else {
      props.variantsController.skipped = false
    }
  }
}

function getVariantsWithoutSelfVariant() {
  return computedVariants.value.filter(variant => !selfVariantChecker(variant))
}

const selfVariant = computed(() => computedVariants.value.find(selfVariantChecker))

const isDropdownWithFiles = computed(() => !props.tabletView && isDropdown.value && toValue(variantsWithFiles))

function onInteractOutside(e) {
  const isFancyboxInteraction = e.target.closest('.fancybox__container')
  if (isFancyboxInteraction) {
    e.preventDefault()
  }
}

function onFocusOutside(e) {
  const isFancyboxInteraction = e.target.closest('.fancybox__container')
  if (isFancyboxInteraction) {
    e.preventDefault()
  }
}

function getMediaDropdownItemClass(option) {
  return isDropdownWithFiles.value && option.id !== 'is_self_answer' ? 'question-variants__file-command-item' : ''
}

const isDropdownSearchable = computed(() => {
  const isDropdownWithFilesOnDesktop = isDropdownWithFiles.value && !isMobile.value
  return !isDropdownWithFilesOnDesktop
})

// Screenshot and file attachment functionality
const shouldShowFileAttachment = computed(() => {
  return questionScreenshot.value && (showScreenshotButton.value || showUploadButton.value)
})

async function handleScreenshotRequest() {
  // Clear previous errors
  props.variantsController.screenshotError.value = null

  try {
    const screenshotData = await simplifiedStore.requestScreenshot()

    if (!screenshotData) {
      props.variantsController.screenshotError.value = 'Не удалось получить данные скриншота'
      return
    }

    if (!screenshotData.html) {
      props.variantsController.screenshotError.value = 'Скриншот не содержит данных'
      return
    }

    props.variantsController.addScreenshot(screenshotData)
  }
  catch (error) {
    console.error('Screenshot failed:', error)

    // Handle specific error types
    if (error.message?.includes('timeout')) {
      props.variantsController.screenshotError.value = 'Время ожидания скриншота истекло'
    }
    else if (error.message?.includes('cancelled')) {
      props.variantsController.screenshotError.value = 'Создание скриншота было отменено'
    }
    else if (error.message?.includes('permission')) {
      props.variantsController.screenshotError.value = 'Недостаточно прав для создания скриншота'
    }
    else {
      props.variantsController.screenshotError.value = 'Не удалось создать скриншот. Попробуйте снова.'
    }
  }
}

function handleFileUpload(files) {
  // Clear previous errors
  props.variantsController.screenshotError.value = null

  if (!files || files.length === 0) {
    props.variantsController.screenshotError.value = 'Не выбраны файлы для загрузки'
    return
  }

  props.variantsController.uploadFiles(files, pollStore.authKey, props.variantsController.data.questionId)
}

function handleRemoveAttachment(index) {
  props.variantsController.removeAttachment(index)
}
</script>

<template>
  <div :class="questionVariantsClasses">
    <div v-if="label" class="question-variants__label">
      <span>{{ label }}</span>
    </div>
    <FormGroup v-if="isSingleOrMultipleType" :error="isInvalid" :error-attrs="{ class: 'question-variants__error' }">
      <div v-if="isDropdown" class="question-variants__select-wrapper">
        <Select
          :model-value="answers"
          :full-width="true" :options="dropdownOptions" :invalid="error"
          :multiple="isMultiple" :item-class="getMediaDropdownItemClass"
          :trigger-class="isDropdownWithFiles ? 'question-variants__file-select-trigger' : ''"
          :searchable="isDropdownSearchable" :search-autofocus="!isMobile" @update:model-value="handleDropdownChange"
          @update:open="onDropdownOpen" @interact-outside="onInteractOutside" @focus-outside="onFocusOutside"
        >
          <template v-if="isDropdownWithFiles" #option="{ option }">
            <div v-if="option.id !== 'is_self_answer'" class="question-variants__file-select-item">
              <template v-if="isMobile">
                <FileCheck
                  :model-value="answers"
                  :value="option.id"
                  :type="inputType"
                  :url="option.url"
                  :poster="option.poster"
                  :label="option.label"
                  :hint="option.description"
                  :group-id="`${groupId}-dropdown-files-options`"
                  :preview-clickable="['video']"
                  @change="onChange"
                />
              </template>
              <template v-else>
                <FilePreview
                  :group="`${groupId}-dropdown-files-options`" view="compact" :full-url="option.url"
                  :preview-url="option.poster" :name="option.label" :type="option.fileType"
                />
                <div class="question-variants__file-select-item-content">
                  <span v-if="option.label" class="question-variants__file-select-item-label">
                    {{ option.label }}
                  </span>
                  <span v-if="option.description" class="question-variants__file-select-item-description">
                    {{ option.description }}
                  </span>
                </div>
              </template>
            </div>
            <template v-else>
              <div class="select-item__label-text">
                {{ option.label }}
              </div>
              <div v-if="option.description" class="select-item__label-description">
                {{ option.description }}
              </div>
            </template>
          </template>
          <template v-if="isDropdownWithFiles" #selectedItem="{ option }">
            <div v-if="option.id !== 'is_self_answer'" class="question-variants__file-select-item">
              <FilePreview
                :group="`${groupId}-dropdown-files-selected`" view="compact" :full-url="option.url" :preview-url="option.poster"
                :name="option.label" :type="option.fileType"
              />
              <div class="question-variants__file-select-item-content">
                <span v-if="option.label" class="question-variants__file-select-item-label">
                  {{ option.label }}
                </span>
                <span v-if="option.description" class="question-variants__file-select-item-description">
                  {{ option.description }}
                </span>
              </div>
            </div>
            <template v-else>
              <div class="select-trigger__label-text">
                {{ option.label }}
              </div>
              <div v-if="option.description" class="select-trigger__label-description">
                {{ option.description }}
              </div>
            </template>
          </template>
        </Select>
        <SelfVariantField :visible="isSelfVariantChecked && shouldShowSelfVariantComment" :variants-controller="variantsController" />
      </div>
      <div
        v-else-if="variantsWithFiles"
        class="question-variants__with-files"
        :class="{ 'question-variants__with-files--gallery': mediaView === 'gallery' }"
      >
        <template v-if="mediaView === 'gallery'">
          <VariantsGallery
            v-model="answers"
            :variants="computedVariants"
            :type="inputType"
            :group-id="`${groupId}-variants-gallery-files`"
            :inactive="isSkipped"
            :tablet-view="tabletView"
            @change="onChange"
          />
          <SelfVariantField
            :visible="isSelfVariantChecked && shouldShowSelfVariantComment"
            :variants-controller="variantsController"
          />
        </template>
        <template v-else>
          <div class="question-variants__file-check-list">
            <div
              v-for="variant in getVariantsWithoutSelfVariant()" :key="variant.value"
              class="question-variants__file-check-list-item"
            >
              <FileCheck
                v-model="answers"
                :value="variant.value"
                :group-id="`${groupId}-files-check`"
                :type="inputType"
                :check-attrs="{ 'data-testid': 'assessments-file-check' }"
                :url="variant.url"
                :poster="variant.poster"
                :inactive="variant.inactive"
                :disabled="variant.disabled"
                :hint="variant.description"
                :label="variant.label"
                :preview-clickable="isMobile ? ['video'] : true"
                @change="onChange"
              />
            </div>
          </div>

          <div v-if="selfVariant?.value" class="question-variants__self-variant-wrapper check-group__item">
            <Check
              v-model="answers"
              :value="selfVariant.value"
              :type="inputType"
              :check-attrs="{ 'data-testid': 'variants-check' }"
              :label="selfVariant.label"
              :invalid="false"
              :inactive="selfVariant.inactive"
              :disabled="selfVariant.disabled"
              :hint="selfVariant.description"
              @change="onChange"
            >
              <template #after>
                <span v-if="selfVariant.description">{{ selfVariant.description }}</span>
              </template>
            </Check>
            <SelfVariantField
              :visible="isSelfVariantChecked && shouldShowSelfVariantComment"
              :variants-controller="variantsController"
            />
          </div>
        </template>
      </div>
      <template v-else>
        <CheckGroup
          v-model="answers" :options="computedVariants" :type="inputType"
          :check-attrs="{ 'data-testid': 'variants-check' }" field="variants"
          @change="onChange"
        >
          <template #after-item="{ option }">
            <SelfVariantField
              v-if="selfVariantChecker(option)"
              :visible="isSelfVariantChecked && shouldShowSelfVariantComment"
              :variants-controller="variantsController"
            />
          </template>
        </CheckGroup>
      </template>
    </FormGroup>
    <FormGroup v-else :error="error">
      <Textarea
        v-model="textFieldValue" min-height="70px" data-testid="assessments-text" :placeholder="placeholderText"
        :minlength="selfVariantMinLength" :maxlength="selfVariantMaxLength" :is-invalid="error"
      />
    </FormGroup>

    <!-- File attachment and screenshot functionality -->
    <div v-if="shouldShowFileAttachment" class="question-variants__file-attachment">
      <VariantsFileAttachment
        :question-screenshot="questionScreenshot"
        :files="files"
        :screenshots="screenshots"
        :error="screenshotError"
        :error-kind="errorKind"
        :is-screenshot-loading="simplifiedStore.isScreenshotLoading"
        :should-show-screenshot-button="showScreenshotButton"
        :button-text="variantsController.fileAttachmentButtonText"
        :screenshot-button-text="variantsController.fileAttachmentScreenshotButtonText"
        @file-upload="handleFileUpload"
        @screenshot-request="handleScreenshotRequest"
        @remove="handleRemoveAttachment"
      />
    </div>
  </div>
</template>

<style scoped>
.question-variants__self-variant-comment {
  padding-top: 0;
}

.question-variants--dropdown .question-variants__self-variant-comment,
.question-variants__with-files .question-variants__self-variant-comment {
  padding-top: 15px;
}

.question-variants__select-wrapper {
  transition: opacity 0.3s;
}

.question-variants__with-files {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

:global(.question-variants__with-files .question-variants__self-variant-comment) {
  padding-top: 0;
}

.question-variants__with-files--gallery {
  gap: 0;
}

:global(.question-variants__with-files--gallery .question-variants__self-variant-comment) {
  margin-top: 0;
  padding-top: 15px;
}

.question-variants__file-check-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 15px 10px;
}

.question-variants__file-check-list-item {
  flex: 0 1 calc(50% - 5px);
  display: flex;
}

.question-variants__label {
  margin-bottom: 15px;
  line-height: 1.1;
  font-size: var(--fqz-poll-font-size);
  overflow: hidden;
}

:global(.question-variants__file-command-item .command-item__content) {
  width: 100%;
}

:global(.question-variants__file-command-item .command-item__content:after) {
  height: calc(100% + 10px);
}

:global(.question-variants__file-command-item) {
  padding-top: 5px;
  padding-bottom: 5px;
  overflow: hidden;
}

:global(.question-variants__file-command-item label) {
  display: flex;
  align-items: center;
}

:global(.question-variants__file-command-item .fc-check__label) {
  padding-top: 0;
}

:global(.question-variants__file-select-item) {
  display: flex;
  flex-direction: row;
  gap: 15px;
  width: 100%;
  align-items: center;
}

:global(.question-variants__file-select-item .file-preview) {
  flex: 0 0 auto;
}

.question-variants__file-select-item-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex-grow: 1;
  min-width: 0;
}

.question-variants__file-select-item-label {
  font-size: 12px;
  line-height: 1.3;
  color: black;
  min-width: 0;
  /* truncate to two lines */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  white-space: normal;
}

.question-variants__file-select-item-description {
  font-size: 12px;
  line-height: 1.1;
  color: rgba(0, 0, 0, 0.6);
  max-width: 100%;
  width: 100%;
}

:deep(.select-trigger .question-variants__file-select-item-description) {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

:deep(.question-variants__error) {
  margin-top: 20px;
}

.question-variants--dropdown :deep(.form-group__error) {
  margin-top: 10px;
}

.question-variants--skipped .question-variants__select-wrapper {
  opacity: 0.7;
}

:global(.question-variants__file-select-trigger .select-trigger__labels) {
  gap: 10px;
}

:global(.question-variants__file-select-trigger .question-variants__file-select-item) {
  gap: 15px;
}

@media (max-width: 679px) {
  .question-variants__label {
    font-size: 14px;
  }

  .question-variants__file-check-list {
    flex-wrap: nowrap;
    flex-direction: column;
  }

  .question-variants__file-check-list-item {
    flex: 0 0 auto;
  }

  :global(.question-variants__file-command-item) {
    padding-top: 7.5px !important;
    padding-bottom: 7.5px !important;
  }

  :global(.question-variants__file-command-item:first-child) {
    padding-top: 0 !important;
  }

  :global(.question-variants__file-command-item:last-child) {
    padding-bottom: 0 !important;
  }

  :global(.question-variants__file-command-item .command-item__content > .fc-check > label > .fc-check__box) {
    display: none;
  }
}

:global(.question-variants__file-command-item .fc-check__label) {
  flex-grow: 1;
}

.question-variants__file-attachment {
  padding-top: 20px;
}
</style>
