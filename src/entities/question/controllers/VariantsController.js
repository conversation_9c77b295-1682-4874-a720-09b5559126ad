import { getBaseAssetsUrl } from '@shared/api'
import { ALLOWED_BUT_NOT_SUPPORTED, ALLOWED_FILE_TYPES } from '@shared/constants/files'
import { getFileTypeFromFilename } from '@shared/helpers/files'
import { declOfNum } from '@shared/helpers/string'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import shuffle from 'lodash.shuffle'
import { nanoid } from 'nanoid'
import { computed, isRef, ref, shallowRef, toValue, watch } from 'vue'
import { uploadFiles } from '../../poll/api'

export class VariantsController {
  constructor(data) {
    this.data = data
    this.translationsStore = useTranslationsStore()
    this.simplifiedStore = useSimplifiedStore()
    this.t = this.translationsStore.t
    this.translations = data.translations
    this.filesizeLimit = 5
    this.filesizeLimitInBytes = (this.filesizeLimit * 1024 * 1024)
    this.enabled = ref(data.enabled)
    this.isRequired = ref(!!data.isRequired)
    this.previousAnswer = ref(data.previousAnswer)
    this.previousAnswerHasSelfVariant = ref(data.previousAnswerHasSelfVariant || false)

    this.previousAnswerItems = (data.previousAnswerItems || []).map(item => Number.parseInt(item)).filter(item => !!item)
    this.rawVariants = data.variants || []

    if (this.previousAnswerHasSelfVariant.value) {
      this.previousAnswerItems.push('is_self_answer')
    }

    this.variantsType = ref(data.variantsType)
    this.hasCustomField = ref(data.hasCustomField)
    this.selfVariantNothing = ref(data.selfVariantNothing)
    this.selfVariantDescription = ref(data.selfVariantDescription || '')
    this.selfVariantFile = shallowRef(data.selfVariantFile || null)
    this.dropdown = ref(data.dropdown)
    this.randomVariantsOrder = ref(data.randomVariantsOrder || false)

    this.multiple = computed(() => this.variantsType.value === 1)
    this.hasUnrequiredField = computed(() => this.dropdown.value && this.multiple.value)

    this._label = ref(data.label)
    this.label = computed(() => {
      return toValue(this.translations).detail_question || this._label.value
    })
    this.selfVariantMinLength = ref(data.selfVariantMinLength)
    this.selfVariantMaxLength = ref(data.selfVariantMaxLength)
    this.selfVariantCommentRequired = ref(data.selfVariantCommentRequired)
    this.isSingle = computed(() => this.variantsType.value === 0)
    this.isMultiple = computed(() => this.variantsType.value === 1)

    this.isSingleOrMultipleType = computed(() => this.isSingle.value || this.isMultiple.value)
    this.isTextType = computed(() => this.variantsType.value === 2)
    this.minChooseVariants = ref(Number.parseInt(data.minChooseVariants) || 0)
    this.answers = shallowRef([])

    // Проверка на выбор варианта с опцией "Ничего из вышеперечисленного"
    this.nothingVariantChosen = computed(() => {
      const answers = toValue(this.answers)

      if (answers.length === 1) {
        const itemOrId = answers[0]
        // answers может быть массивом из объектов или массивом из id
        // Объекты могут приходить из выпадающего списка, если this.dropdown = true
        const answersContainObject = answers.some?.(answer => typeof answer === 'object')
        const item = this.variants.value.find(v => v.id === (answersContainObject ? itemOrId.id : itemOrId))
        if (item) {
          return item.type === 1
        }
      }

      return false
    })

    this.minChooseVariantsEnabled = computed(() => this.minChooseVariants.value > 0 && this.isMultiple.value && !this.nothingVariantChosen.value)
    this.maxChooseVariants = ref(data.maxChooseVariants || Infinity)
    this.answersCountLimit = computed(() => this.maxChooseVariants.value)
    this.variantsWithFiles = ref(data.variantsWithFiles || false)
    this.showTooltips = ref(data.showTooltips || false)

    this.skipped = computed({
      get() {
        return toValue(data.skipped)
      },
      set(value) {
        if (isRef(data.skipped)) {
          data.skipped.value = value
        }
        else if (data.skipped) {
          data.skipped = value
        }
      },
    })

    const filteredVariants = this.rawVariants.filter(v => v.is_deleted === 0)

    const transformedVariants = this.getTransformedVariants(filteredVariants)

    this.variants = ref(transformedVariants)

    watch(this.answers, (newAnswers, oldAnswers) => {
      if (!Array.isArray(newAnswers) || newAnswers.length === 0 || newAnswers.length === 1) {
        return
      }

      const answersContainObject = newAnswers.some(answer => typeof answer === 'object')

      // Find the last added variant by comparing new and old arrays
      const lastAddedAnswer = newAnswers.find(answer => !oldAnswers.includes(answer))

      if (!lastAddedAnswer)
        return

      const lastAnswerId = answersContainObject ? lastAddedAnswer.id : lastAddedAnswer
      const lastVariant = this.variants.value.find(v => v.id === lastAnswerId)

      if (!lastVariant) {
        return
      }

      if (lastVariant.type === 1) {
        // If the last added variant has type 1, remove all other variants
        // This means the "None of the above" option is enabled for this variant
        this.answers.value = [lastAddedAnswer]
      }
      else if (lastVariant.type === 0) {
        // If the last added variant has type 0, check if there's a variant of type 1
        const type1Answer = newAnswers.find((answer) => {
          if (answersContainObject) {
            return answer.type === 1
          }
          const answerId = answer
          return this.variants.value.find(v => v.id === answerId && v.type === 1)
        })
        if (type1Answer) {
          const shouldAddId = answersContainObject && !toValue(this.dropdown)
          // If there's a variant of type 1, remove it and keep only the last added variant
          this.answers.value = [shouldAddId ? lastAddedAnswer.id : lastAddedAnswer]
        }
      }
    })

    this.touched = ref(false)
    this.selfVariantCommentTouched = ref(false)
    this.error = ref(null)
    this.selfVariantCommentError = ref(null)
    this.textFieldValue = ref(data.textFieldValue || '')
    this.textFieldParam = data.textFieldParam || {}

    // Screenshot and file attachment functionality
    this.questionScreenshot = ref(data.questionScreenshot || null)
    this.screenshots = ref([])
    this.files = ref([])
    this.screenshotError = ref(null)
    this.errorKind = ref('default')

    const _placeholderText = ref(data.placeholderText)
    this.placeholderText = computed({
      get: () => {
        return toValue(this.translations)?.placeholder_text || _placeholderText.value
      },
      set: (value) => {
        _placeholderText.value = value
      },
    })

    const _selfVariantPlaceholderText = ref(data.selfVariantPlaceholderText)
    this.selfVariantPlaceholderText = computed({
      get: () => {
        const translations = toValue(this.translations)
        const placeholderText = translations?.self_variant_placeholder_text || translations?.placeholder_text
        return placeholderText || _selfVariantPlaceholderText.value
      },
      set: (value) => {
        _selfVariantPlaceholderText.value = value
      },
    })
    this.selfVariantPosition = ref(data.selfVariantPosition || null)
    this.customVariantName = ref(toValue(data.selfVariantText) || 'Свой вариант')
    this.customFieldBlocked = ref(false)

    this.answers.value = this.preparePreviousAnswers()

    this.selectedAndUnselectedVariants = computed(() => {
      const answers = toValue(this.answers)

      const normalizedAnswers = Array.isArray(answers) ? answers : [answers]

      const answersContainObject = normalizedAnswers.some?.(answer => typeof answer === 'object')
      const allVariants = this.variants.value
      const selectedVariants = allVariants.filter((v) => {
        if (answersContainObject) {
          return normalizedAnswers.some(answer => answer.id === v.id)
        }
        return normalizedAnswers.includes(v.id)
      })
      const unselectedVariants = allVariants.filter((v) => {
        if (answersContainObject) {
          return !normalizedAnswers.some(answer => answer.id === v.id)
        }
        return !normalizedAnswers.includes(v.id)
      })

      return {
        selected: selectedVariants,
        unselected: unselectedVariants,
      }
    })

    this.hasSelectedVariants = computed(() => {
      return toValue(this.selectedAndUnselectedVariants).selected.length > 0
    })

    this.hasUnselectedVariants = computed(() => {
      return toValue(this.selectedAndUnselectedVariants).unselected.length > 0
    })

    this.shouldShowSelfVariantComment = ref(true)

    this.selfVariantComment = ref(this.textFieldValue.value || '')

    // Проверка на наличие выбора "Свой вариант"
    this.isSelfVariantChecked = computed(() => {
      const answers = this.answers.value
      if (!answers || !this.shouldShowSelfVariantComment.value)
        return false

      const isAnswersArray = Array.isArray(answers)
      const answersContainObject = answers?.some?.(answer => typeof answer === 'object')

      // Если answers - это массив, содержащий объекты, то проверяем наличие 'is_self_answer' в массиве
      // Объекты могут приходить из выпадающего списка, если this.dropdown = true
      if (isAnswersArray && answersContainObject) {
        return answers.some(answer => answer.id === 'is_self_answer')
      }
      // Если answers - это массив, то проверяем наличие 'is_self_answer' в массиве
      if (isAnswersArray) {
        return answers.length > 0 && answers.includes('is_self_answer')
      }
      // Если answers - это строка, то проверяем наличие 'is_self_answer' в строке
      return answers === 'is_self_answer'
    })

    this.emptyVariants = computed(() => this.variants.value.filter(v => v.type === 1))

    this.showCustomField = computed(() => {
      if (!this.hasCustomField.value)
        return false
      if (!this.dropdown.value)
        return this.isSelfVariantChecked.value
      if (!this.isSelfVariantChecked.value) {
        this.textFieldValue.value = ''
      }
      return this.isSelfVariantChecked.value
    })

    // Screenshot and file attachment computed properties
    this.showScreenshotButton = computed(() => {
      return this.questionScreenshot.value
        && this.questionScreenshot.value.make_screenshot_enabled === 1
        && this.simplifiedStore.isSimplifiedMode
        && this.simplifiedStore.isInIframe
    })

    this.showUploadButton = computed(() => {
      return this.questionScreenshot.value && this.questionScreenshot.value.upload_enabled === 1
    })

    this.maxFiles = computed(() => {
      return this.questionScreenshot.value ? this.questionScreenshot.value.max_files : 0
    })

    this.allAttachments = computed(() => {
      return [...this.files.value, ...this.screenshots.value]
    })

    // Computed properties for translated button texts
    this.fileAttachmentButtonText = computed(() => {
      return this.getTranslatedQuestionScreenshotText('button_text', 'Прикрепить файл')
    })

    this.fileAttachmentScreenshotButtonText = computed(() => {
      return this.getTranslatedQuestionScreenshotText('screenshot_button_text', 'Сделать скриншот')
    })

    this.hasCustomAnswer = computed(() => {
      if (this.hasUnrequiredField.value)
        return this.textFieldValue.value
      if (this.isSelfVariantChecked.value) {
        return this.textFieldValue.value
      }
      return false
    })

    this.answersIds = computed(() => {
      const answers = toValue(this.answers)
      const answersContainObject = answers.some?.(answer => typeof answer === 'object')
      if (answersContainObject) {
        return answers.map(answer => answer.id)
      }
      return answers
    })

    this.variantsWithCommentRequired = computed(() => {
      return this.variants.value.filter(v => v.commentRequired)
    })

    this.answersCount = computed(() => {
      return this.answers.value.length
    })

    this.blocked = computed(() => {
      const isBlocked = this.answersCount.value >= this.answersCountLimit.value
      if (isBlocked) {
        if (this.hasUnrequiredField.value && !this.isSelfVariantChecked.value) {
          this.customFieldBlocked.value = true
        }
      }
      else {
        this.customFieldBlocked.value = false
      }
      return isBlocked
    })

    this.isValid = computed(() => {
      if (!this.touched.value || this.skipped.value)
        return true
      return this.checkValidity()
    })

    watch(this.skipped, (v) => {
      if (v) {
        this.resetFields()
      }
    })

    watch(this.textFieldValue, () => {
      this.validate()
    })

    watch(this.selfVariantComment, () => {
      this.validate()
    })

    watch(this.answers, () => {
      this.validate()
    })
  }

  getTransformedVariants(variants) {
    const getDisabledComputed = (v) => {
      return computed(() => {
        if (!Array.isArray(this.answers.value)) {
          return false
        }

        if (this.isSingle.value) {
          return false
        }

        const answersContainObject = this.answers.value.some?.(answer => typeof answer === 'object')
        const reachedLimit = this.answersCount.value >= this.answersCountLimit.value

        // answers может содержать объекты, если это выпадающий список
        if (answersContainObject) {
          const isSelected = this.answers.value.some(answer => answer.id === v.id)
          return reachedLimit && !isSelected
        }

        const isSelected = this.answers.value.includes(v.id)
        return reachedLimit && !isSelected
      })
    }

    const getDescriptionComputed = (v) => {
      return computed(() => {
        if (!toValue(this.showTooltips))
          return null
        const vId = v.id
        const description = this.translations.value?.detailLangs?.[vId]?.description || v.description
        return description
      })
    }

    let lastVariantPosition = 0

    let transformedVariants = variants.map((v) => {
      const vId = v.id
      const transformedVariant = {
        ...v,
        text: computed(() => {
          const translation = toValue(this.translations).detailLangs?.[vId]
          const val = toValue(v.question) || toValue(v.variant) || toValue(v.value)
          return translation?.question || val
        }),
        disabled: getDisabledComputed(v),
        inactive: computed(() => {
          return toValue(this.skipped)
        }),
        label: computed(() => {
          const translation = toValue(this.translations).detailLangs?.[vId]
          const val = toValue(v.question) || toValue(v.variant) || toValue(v.value)
          return translation?.question || val
        }),
        rawLabel: v.variant || v.value || toValue(v.question),
        value: v.id,
        commentRequired: toValue(v.comment_required),
      }

      transformedVariant.alternativeLabel = computed(() => {
        const label = toValue(transformedVariant.label)
        if (label) {
          return label
        }
        return v.position
      })

      transformedVariant.alternativeVariantLabel = computed(() => {
        const label = toValue(transformedVariant.label)
        if (label) {
          return label
        }
        const variantWordTranslation = toValue(this.t('Вариант'))

        return `${variantWordTranslation} ${v.position}`
      })

      /**
       * Возвращает альтернативное "сырое" название варианта
       * Например, если rawLabel - это пустая строка, то возвращается "Вариант 1"
       * В некоторых случаях нам нужно иметь доступ к оригинальному названию варианта
       * БЕЗ перевода. Например, в матричных вопросах УВ может приходить
       * для определенных строк и колонок, но названия приходят не в id,
       * а в виде строки с названием варианта
       * (не спрашивайте меня почему так, сам не знаю). Поэтому нам нужно иметь возможность
       * получить оригинальное название варианта
       */
      transformedVariant.alternativeRawLabel = computed(() => {
        const rawLabel = toValue(transformedVariant.rawLabel)
        if (rawLabel) {
          return rawLabel
        }
        return `Вариант ${v.position}`
      })

      if (toValue(this.variantsWithFiles) && v.file_url) {
        const normalizedUrl = getBaseAssetsUrl() + v.file_url
        transformedVariant.url = normalizedUrl
      }

      if (toValue(this.variantsWithFiles) && v.preview_url) {
        const normalizedUrl = getBaseAssetsUrl() + v.preview_url
        transformedVariant.poster = normalizedUrl
      }

      if (toValue(this.variantsWithFiles) && v.file_url) {
        const fileType = getFileTypeFromFilename(v.file_url)
        transformedVariant.fileType = fileType
      }

      if (toValue(this.showTooltips)) {
        transformedVariant.description = getDescriptionComputed(v)
      }

      if (v.position > lastVariantPosition) {
        lastVariantPosition = v.position
      }

      return transformedVariant
    })

    let customVariant = null
    if (this.hasCustomField.value) {
      customVariant = {
        id: 'is_self_answer',
        value: toValue(this.customVariantValue),
        selfVariantValue: computed(() => {
          return toValue(this.selfVariantComment)
        }),
        comment_required: toValue(this.selfVariantCommentRequired) ? 1 : 0,
        text: computed(() => {
          const textFromTranslations = toValue(this.translations).self_variant_text
          if (textFromTranslations) {
            return textFromTranslations
          }
          return toValue(this.customVariantName)
        }),
        type: this.selfVariantNothing.value ? 1 : 0,
        position: toValue(this.selfVariantPosition) || lastVariantPosition + 1,
        inactive: computed(() => {
          return toValue(this.skipped)
        }),
        commentRequired: toValue(this.selfVariantCommentRequired),
      }

      // Add file properties if self_variant_file exists
      if (this.selfVariantFile.value) {
        const { file_id, file_url, preview_url } = this.selfVariantFile.value
        customVariant.file_id = file_id
        if (file_url) {
          customVariant.url = getBaseAssetsUrl() + file_url
        }
        if (preview_url && preview_url !== '/uploads/') {
          customVariant.poster = getBaseAssetsUrl() + preview_url
        }
        if (file_url) {
          customVariant.fileType = getFileTypeFromFilename(file_url)
        }
      }

      customVariant.label = customVariant.text
      customVariant.alternativeRawLabel = computed(() => {
        return toValue(this.customVariantName)
      })
      customVariant.disabled = getDisabledComputed(customVariant)
      if (toValue(this.showTooltips)) {
        customVariant.description = computed(() => {
          const descriptionFromTranslations = toValue(this.translations).self_variant_description
          if (descriptionFromTranslations) {
            return descriptionFromTranslations
          }
          return toValue(this.selfVariantDescription) || ''
        })
      }
      customVariant.alternativeVariantLabel = computed(() => {
        return toValue(this.selfVariantComment || '')
      })
    }

    // @NOTE: Если есть вопрос-донор, то добавляем свой вариант
    // ДО перемешивания вариантов. Таким образом на него  будет тоже действовать сортировка
    if (customVariant && this.hasDonor) {
      transformedVariants.push(customVariant)
    }

    transformedVariants = this.sortVariants(transformedVariants)

    if (customVariant && !this.hasDonor) {
      transformedVariants.push(customVariant)
    }

    return transformedVariants
  }

  sortVariants(variants) {
    if (this.randomVariantsOrder.value) {
      const shuffledVariants = shuffle(variants)
      const exclusionedVariants = shuffledVariants.filter(({ random_exclusion }) => !!random_exclusion)
      if (exclusionedVariants.length) {
        const sortedExclusionedVariants = exclusionedVariants.sort((a, b) => a.position - b.position)
        const unexclusionedVariants = shuffledVariants.filter(({ random_exclusion }) => !random_exclusion)
        sortedExclusionedVariants.forEach((variant) => {
          const { position } = variant
          unexclusionedVariants.splice(position - 1, 0, variant)
        })
        return unexclusionedVariants
      }
      else {
        return shuffledVariants
      }
    }
    return variants.sort((a, b) => a.position - b.position)
  }

  preparePreviousAnswers() {
    if (this.dropdown.value) {
      let items = this.previousAnswerItems || []

      items = items.filter(id => !!id)

      // В случае, если у нас включен выпадающий список, то мы должны вернуть массив объектов
      // Это нужно для селекта, чтобы он мог отобразить выбранные варианты
      items = items.map(id => this.variants.value.find(v => Number(v.id) === Number(id))).filter(v => v)

      return items
    }
    else if (this.isMultiple.value) {
      return this.previousAnswerItems || []
    }

    return this.previousAnswerItems?.[0] || ''
  }

  getDropdownOptions() {
    if (!this.dropdown.value) {
      return []
    }
    const options = this.variants.value.map(v => ({
      id: v.id,
      value: v.id,
      label: toValue(v.text),
      text: toValue(v.text),
      disabled: v.disabled,
      url: v.url,
      poster: v.poster,
      fileType: v.fileType,
      description: toValue(this.showTooltips) ? v.description : null,
      type: v.type,
    }))

    return options
  }

  handleDropdownChange(selectedOptions) {
    if (Array.isArray(selectedOptions)) {
      this.answers.value = selectedOptions
    }
    else {
      this.answers.value = [selectedOptions]
    }
    this.validate()
  }

  isVariantDisabled(variantId) {
    if (!this.isMultiple.value || this.answersCount.value < this.answersCountLimit.value) {
      return false
    }
    return !this.answers.value.includes(variantId)
  }

  resetFields() {
    this.textFieldValue.value = ''
    this.selfVariantComment.value = ''
    this.selfVariantCommentTouched.value = false
    this.selfVariantCommentError.value = null
    this.resetAnswers()
  }

  resetAnswers() {
    this.touched.value = false
    this.error.value = null
    this.answers.value = this.variantsType.value === 1 ? [] : ''
  }

  validateSelfVariantComment() {
    this.selfVariantCommentError.value = null

    if (!this.isSelfVariantChecked.value) {
      return true
    }

    if (!this.selfVariantCommentTouched.value) {
      return true
    }

    if (this.selfVariantComment.value.length === 0) {
      this.selfVariantCommentError.value = this.translationsStore.t('Обязательное поле')
      return false
    }

    if (this.selfVariantComment.value.length < this.selfVariantMinLength.value) {
      const count = this.selfVariantMinLength.value
      const word = this.getSymbolWordForm(count)
      const characters = toValue(this.translationsStore.t(`{count} ${word}`, { count }))
      this.selfVariantCommentError.value = this.translationsStore.t(`Должно быть введено хотя бы {characters}`, { characters })
      return false
    }
    if (this.selfVariantComment.value.length > this.selfVariantMaxLength.value) {
      const count = this.selfVariantMaxLength.value
      const word = this.getSymbolWordForm(count)
      const characters = toValue(this.translationsStore.t(`{count} ${word}`, { count }))
      this.selfVariantCommentError.value = this.translationsStore.t(`Должно быть введено не более {characters}`, { characters })
      return false
    }

    return true
  }

  validate() {
    if (!toValue(this.enabled) || this.skipped.value || !this.touched.value) {
      return true
    }
    if (this.isTextType.value) {
      return this.validateTextField()
    }
    return this.validateVariants()
  }

  validateTextField() {
    this.error.value = null

    if (this.skipped.value)
      return true
    if (!this.enabled.value)
      return true

    if (toValue(this.isRequired) && this.textFieldValue.value.length === 0) {
      this.error.value = this.translationsStore.t('Обязательное поле')
      return false
    }

    const hasSomeValue = this.textFieldValue.value.length > 0
    const notEnoughCharacters = this.textFieldValue.value.length < this.selfVariantMinLength.value

    if (hasSomeValue && notEnoughCharacters) {
      const count = this.selfVariantMinLength.value
      const word = this.getSymbolWordForm(count)
      const characters = toValue(this.translationsStore.t(`{count} ${word}`, { count }))
      this.error.value = this.translationsStore.t(`Должно быть введено хотя бы {characters}`, { characters })
      return false
    }

    const tooManyCharacters = this.textFieldValue.value.length > this.selfVariantMaxLength.value
    if (hasSomeValue && tooManyCharacters) {
      const count = this.selfVariantMaxLength.value
      const word = this.getSymbolWordForm(count)
      const characters = toValue(this.translationsStore.t(`{count} ${word}`, { count }))
      this.error.value = this.translationsStore.t(`Должно быть введено не более {characters}`, { characters })
      return false
    }

    return true
  }

  validateVariants() {
    let valid = true
    let selfVariantCommentValid = true

    this.error.value = null
    this.selfVariantCommentError.value = null

    if (this.skipped.value)
      return true
    if (!this.enabled.value)
      return true

    const hasCheckedVariant = Array.isArray(this.answers.value) ? this.answers.value.length > 0 : !!this.answers.value
    const isRequired = toValue(this.isRequired)
    const invalidLimit = this.answersCount.value < this.minChooseVariants.value
    if (this.minChooseVariantsEnabled.value && (isRequired || (!isRequired && hasCheckedVariant))) {
      const onlySelfVariantChecked = this.answersCount.value === 1 && this.isSelfVariantChecked.value
      const errorFirstPart = this.translationsStore.t('Необходимо выбрать хотя бы')
      const symbolWordForm = this.getVariantsWordForm(this.minChooseVariants.value)
      const errorSecondPart = this.translationsStore.t(`{count} ${symbolWordForm}`, { count: this.minChooseVariants.value, word: symbolWordForm })

      if (onlySelfVariantChecked) {
        selfVariantCommentValid = this.validateSelfVariantComment()
      }

      if (invalidLimit && selfVariantCommentValid) {
        this.error.value = `${errorFirstPart.value} ${errorSecondPart.value}`
        valid = false
      }
    }

    const shouldShowError = isRequired && !hasCheckedVariant && valid
    if (shouldShowError) {
      const errorText = this.dropdown.value
        ? this.translationsStore.t('Обязательное поле')
        : this.translationsStore.t('Нужно выбрать один из вариантов')
      this.error.value = errorText
      valid = false
    }

    selfVariantCommentValid = this.validateSelfVariantComment()

    return valid && selfVariantCommentValid
  }

  checkValidity() {
    return this.validate()
  }

  getVariantsWordForm(n) {
    return declOfNum(n, ['вариант', 'варианта', 'вариантов'])
  }

  getSymbolWordForm(n) {
    return declOfNum(n, ['символ', 'символа', 'символов'])
  }

  toggleVariant(variant, isChecked) {
    this.skipped.value = false

    if (variant.type === 1 || (variant.id === 'is_self_answer' && this.selfVariantNothing.value)) {
      this.answers.value = isChecked ? [variant.id] : []
      this.variants.value.filter(v => v.id !== variant.id).forEach((v) => {
        v.isChecked.value = false
      })
    }
    else if (this.variantsType.value === 1) {
      this.answers.value = isChecked ? [variant.id] : []
      if (this.variants.value.some(v => v.type === 1 || (v.id === 'is_self_answer' && this.selfVariantNothing.value))) {
        this.variants.value.filter(v => v.type === 1 || (v.id === 'is_self_answer' && this.selfVariantNothing.value)).forEach(v => v.isChecked.value = false)
      }
    }
    else {
      if (!isChecked)
        return
      this.answers.value.push(variant.id)
    }
  }

  getData() {
    const answers = toValue(this.answers)
    const normalizedAnswers = Array.isArray(answers) ? answers : [answers]

    const getDetailItemArray = () => {
      const ids = this.dropdown.value
        ? normalizedAnswers.map(item => item.id)
        : normalizedAnswers

      return ids
    }

    const data = {
      detail_item: getDetailItemArray(),
    }

    if (this.isSelfVariantChecked.value) {
      if (this.dropdown.value && this.variantsType.value === 1) {
        data.detail_item.push('is_self_answer')
      }
      data.self_variant = this.selfVariantComment.value
    }

    if (this.variantsType.value === 2) {
      data.textAnswer = this.textFieldValue.value
    }

    return data
  }

  get hasValue() {
    const answers = toValue(this.answers)
    if (this.isTextType.value) {
      return toValue(this.textFieldValue).length > 0
    }
    return Array.isArray(answers) ? answers.length > 0 : !!answers || this.textFieldValue.value
  }

  updateFromPreview(data) {
    if (!data) {
      return
    }

    const variantsTypeChanged = this.variantsType.value !== data.variantsType
    const dropdownChanged = this.dropdown.value !== data.dropdown

    const minChooseVariantsValue = Number.parseInt(data.minChooseVariants) || 0
    const maxChooseVariantsValue = Number.parseInt(data.maxChooseVariants) || Infinity
    const minChooseVariantsChanged = this.minChooseVariants.value !== minChooseVariantsValue
    const maxChooseVariantsChanged = this.maxChooseVariants.value !== maxChooseVariantsValue

    if (data.isRequired !== undefined) {
      this.isRequired.value = data.isRequired
    }

    if (data.label !== undefined) {
      this._label.value = data.label
    }

    if (data.variantsType !== undefined)
      this.variantsType.value = data.variantsType

    if (data.hasCustomField !== undefined)
      this.hasCustomField.value = data.hasCustomField

    if (data.selfVariantNothing !== undefined)
      this.selfVariantNothing.value = data.selfVariantNothing

    if (data.dropdown !== undefined)
      this.dropdown.value = data.dropdown

    if (data.minChooseVariants !== undefined)
      this.minChooseVariants.value = minChooseVariantsValue

    if (data.maxChooseVariants !== undefined)
      this.maxChooseVariants.value = maxChooseVariantsValue

    if (data.randomVariantsOrder !== undefined)
      this.randomVariantsOrder.value = data.randomVariantsOrder

    if (data.textFieldParam !== undefined) {
      this.textFieldParam = data.textFieldParam
      this.selfVariantMinLength.value = data.textFieldParam.min
      this.selfVariantMaxLength.value = data.textFieldParam.max
    }

    if (data.selfVariantMinLength !== undefined)
      this.selfVariantMinLength.value = data.selfVariantMinLength

    if (data.selfVariantMaxLength !== undefined)
      this.selfVariantMaxLength.value = data.selfVariantMaxLength

    if (data.selfVariantCommentRequired !== undefined)
      this.selfVariantCommentRequired.value = data.selfVariantCommentRequired

    if (data.placeholderText !== undefined)
      this.placeholderText.value = data.placeholderText

    if (data.selfVariantPlaceholderText !== undefined)
      this.selfVariantPlaceholderText.value = data.selfVariantPlaceholderText

    if (data.selfVariantPosition !== undefined)
      this.selfVariantPosition.value = data.selfVariantPosition

    if (data.selfVariantDescription !== undefined)
      this.selfVariantDescription.value = data.selfVariantDescription

    const correctSelfVariantFileUrl = data.selfVariantFile?.fileUrl || data.selfVariantFile?.file_url
    const correctSelfVariantFilePreviewUrl = data.selfVariantFile?.previewUrl || data.selfVariantFile?.preview_url
    const correctSelfVariantFileId = data.selfVariantFile?.id || data.selfVariantFile?.file_id
    const hasCorrectSelfVariantFile = correctSelfVariantFileUrl && correctSelfVariantFilePreviewUrl && correctSelfVariantFileId
    if (hasCorrectSelfVariantFile) {
      this.selfVariantFile.value = {
        file_url: correctSelfVariantFileUrl,
        preview_url: correctSelfVariantFilePreviewUrl,
        file_id: correctSelfVariantFileId,
      }
    }
    else {
      this.selfVariantFile.value = null
    }

    this.customVariantName.value = data.selfVariantText || 'Свой вариант'

    if (data.variantsWithFiles !== undefined)
      this.variantsWithFiles.value = data.variantsWithFiles

    if (data.showTooltips !== undefined)
      this.showTooltips.value = data.showTooltips

    if (data.skipped !== undefined)
      this.skipped.value = data.skipped

    const isVariantChanged = (originalVariants, newVariants) => {
      if (!originalVariants || !newVariants)
        return false
      return originalVariants.length !== newVariants.length
        || originalVariants.some((v, index) =>
          v.id !== newVariants[index].id
          || toValue(v.text) !== toValue(newVariants[index].text)
          || toValue(v.url) !== toValue(newVariants[index].url)
          || toValue(v.description) !== toValue(newVariants[index].description),
        )
    }

    if (data.variants && Array.isArray(data.variants)) {
      const filteredVariants = data.variants.map((v, index) => ({
        ...v,
        question: v.value,
        position: index + 1,
        id: v.persistentId || v.id,
        preview_url: v.preview_url || v.file?.previewUrl,
        file_url: v.file_url || v.file?.fileUrl,
      }))

      const variantsHaveSelfVariant = filteredVariants.some(v => v.id === 'is_self_answer')

      if (variantsHaveSelfVariant) {
        this.shouldShowSelfVariantComment.value = false
      }

      this.variants.value = this.getTransformedVariants(filteredVariants)

      if (this.dropdown.value) {
        const previousVariants = this.variants.value
        const changed = isVariantChanged(previousVariants, this.variants.value)
        if (changed) {
          this.resetAnswers()
        }
      }
    }

    if (variantsTypeChanged || dropdownChanged || minChooseVariantsChanged || maxChooseVariantsChanged) {
      this.resetFields()
    }
  }

  get variantsInputType() {
    return this.variantsType.value === 0 ? 'radio' : 'checkbox'
  }

  get variantsPrefix() {
    return this.variantsType.value === 0 ? 'f-radio' : 'f-check'
  }

  get variantsInputClass() {
    return this.variantsType.value === 0 ? 'form-radio' : 'form-check-input'
  }

  // Helper method for getting translated questionScreenshot text
  getTranslatedQuestionScreenshotText(fieldName, defaultText) {
    const questionScreenshot = this.questionScreenshot.value
    if (!questionScreenshot) {
      return this.t(defaultText)
    }

    // Check translations first
    const translatedText = toValue(this.translations)?.[fieldName]
    if (translatedText) {
      return translatedText
    }

    // Fall back to questionScreenshot field or default
    return questionScreenshot[fieldName] || this.t(defaultText)
  }

  // Screenshot and file attachment methods
  addScreenshot(screenshotData) {
    if (this.allAttachments.value.length >= this.maxFiles.value) {
      this.screenshotError.value = `Максимальное количество файлов: ${this.maxFiles.value}`
      return
    }

    const screenshot = {
      uuid: nanoid(),
      id: Date.now().toString(),
      type: 'screenshot',
      name: `Screenshot ${new Date(screenshotData.timestamp).toLocaleString()}`,
      timestamp: screenshotData.timestamp,
      html: screenshotData.html,
      selection: screenshotData.selection,
      client: screenshotData.client,
      isMobile: screenshotData.isMobile,
      isUploading: false,
    }

    this.screenshots.value.push(screenshot)
    this.screenshotError.value = null
  }

  addFile(fileData) {
    if (this.allAttachments.value.length >= this.maxFiles.value) {
      this.screenshotError.value = `Максимальное количество файлов: ${this.maxFiles.value}`
      return
    }

    const file = {
      uuid: nanoid(),
      id: Date.now().toString(),
      type: 'file',
      name: fileData.name,
      file: fileData,
      isUploading: false,
      previewUrl: fileData.previewUrl || null,
      fullUrl: fileData.fullUrl || null,
    }

    this.files.value.push(file)
    this.screenshotError.value = null
  }

  // returns 'image', 'video' or 'audio' or null
  getFileType(file) {
    const regex = /\.[0-9a-z]+$/i
    const fileExtension = file.name.match(regex)?.[0]?.slice(1).toLowerCase()

    // Check in ALLOWED_FILE_TYPES
    for (const [fileType, extensions] of Object.entries(ALLOWED_FILE_TYPES)) {
      if (extensions.includes(fileExtension)) {
        return fileType
      }
    }

    // Check in ALLOWED_BUT_NOT_SUPPORTED
    for (const [fileType, extensions] of Object.entries(ALLOWED_BUT_NOT_SUPPORTED)) {
      if (extensions.includes(fileExtension)) {
        return fileType
      }
    }

    return null
  }

  isFileTypeAllowed(file) {
    // First check browser's MIME type
    if (file.type && file.type.startsWith('image/')) {
      return true
    }

    // Fallback to extension check
    const fileType = this.getFileType(file)
    return fileType === 'image'
  }

  showTemporaryError(message, kind = 'default') {
    this.screenshotError.value = message
    this.errorKind.value = kind
    setTimeout(() => {
      if (this.errorKind.value === kind) {
        this.screenshotError.value = null
      }
    }, 3000)
  }

  async uploadFiles(files, authKey, questionId) {
    if (!files || files.length === 0) {
      return
    }

    this.screenshotError.value = null

    const remainingSlots = this.maxFiles.value - this.allAttachments.value.length
    if (remainingSlots <= 0) {
      this.showTemporaryError(this.t(`Максимальное количество файлов: ${this.maxFiles.value}`))
      return
    }

    // Validate files
    const validFiles = []
    const invalidFiles = []

    for (const file of files) {
      if (!this.isFileTypeAllowed(file)) {
        invalidFiles.push({ file, reason: 'type' })
      }
      else if (file.size > this.filesizeLimitInBytes) {
        invalidFiles.push({ file, reason: 'size' })
      }
      else {
        validFiles.push(file)
      }
    }

    // Show error for invalid files
    if (invalidFiles.length > 0) {
      const typeErrors = invalidFiles.filter(f => f.reason === 'type')
      const sizeErrors = invalidFiles.filter(f => f.reason === 'size')

      if (typeErrors.length > 0) {
        this.showTemporaryError(this.t('Недопустимый тип файла'))
      }

      if (sizeErrors.length > 0) {
        const fileNames = sizeErrors.map(f => f.file.name)
        const firstTranslation = this.t('Файл слишком большой')
        const secondTranslation = this.t('Размер файла не должен превышать {size} Мб', { size: this.filesizeLimit })
        const message = computed(() => `${firstTranslation.value}: «${fileNames.join('», «')}». ${secondTranslation.value}`)
        this.showTemporaryError(message, 'too-large')
      }
    }

    if (validFiles.length === 0) {
      this.showTemporaryError(this.t('Нет файлов для загрузки'))
      return
    }

    const filesToUpload = validFiles.slice(0, remainingSlots)

    try {
      // Add files with uploading state and UUID
      const uploadingFiles = filesToUpload.map(file => ({
        uuid: nanoid(),
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        type: 'image',
        name: file.name,
        file,
        isUploading: true,
        previewUrl: this.createPreviewUrl(file),
        fullUrl: null,
      }))

      this.files.value.push(...uploadingFiles)

      // Prepare UUIDs array for API call
      const uuids = uploadingFiles.map(f => f.uuid)

      // Make actual API call
      const { files: uploadedFiles } = await uploadFiles({
        authKey: this.data.isPreviewMode ? 'preview' : authKey,
        files: filesToUpload,
        questionId: questionId || this.data.question_id,
        isPreview: this.data.isPreviewMode || false,
        uuids,
      })

      // Update files with server response using UUID for identification
      uploadingFiles.forEach((uploadingFile) => {
        const fileIndex = this.files.value.findIndex(f => f.uuid === uploadingFile.uuid)
        if (fileIndex !== -1) {
          // Find the uploaded file by UUID from server response
          const uploadedFile = uploadedFiles.find(f => f.uuid === uploadingFile.uuid)

          if (uploadedFile) {
            this.files.value[fileIndex] = {
              ...this.files.value[fileIndex],
              id: uploadedFile.id,
              isUploading: false,
              previewUrl: uploadedFile.image ? `${getBaseAssetsUrl()}${uploadedFile.image}` : null,
              fullUrl: uploadedFile.link ? `${getBaseAssetsUrl()}${uploadedFile.link}` : null,
              serverData: uploadedFile,
            }
          }
        }
      })
    }
    catch (error) {
      console.error('File upload error:', error)

      // Handle 413 error specifically
      if (error.status === 413) {
        this.showTemporaryError(this.t('Произошла ошибка, попробуйте ещё раз'))
      }
      else {
        this.showTemporaryError(this.t('Не удалось загрузить файлы'))
      }

      // Remove uploading files on error
      this.files.value = this.files.value.filter(f => !f.isUploading)
    }
  }

  createPreviewUrl(file) {
    if (file.type.startsWith('image/')) {
      return URL.createObjectURL(file)
    }
    return null
  }

  removeAttachment(index) {
    const attachment = this.allAttachments.value[index]
    if (!attachment)
      return

    if (attachment.type === 'screenshot') {
      const screenshotIndex = this.screenshots.value.findIndex(s => s.id === attachment.id)
      if (screenshotIndex !== -1) {
        this.screenshots.value.splice(screenshotIndex, 1)
      }
    }
    else {
      const fileIndex = this.files.value.findIndex(f => f.id === attachment.id)
      if (fileIndex !== -1) {
        this.files.value.splice(fileIndex, 1)
      }
    }

    this.screenshotError.value = null
  }

  clearScreenshotError() {
    this.screenshotError.value = null
  }

  getScreenshotData() {
    return {
      screenshots: this.screenshots.value.map(screenshot => ({
        uuid: screenshot.uuid,
        html: screenshot.html,
        timestamp: screenshot.timestamp,
        selection: screenshot.selection,
        client: screenshot.client,
        isMobile: screenshot.isMobile,
      })),
      files: this.files.value.map(file => ({
        uuid: file.uuid,
        name: file.name,
        file: file.file,
        previewUrl: file.previewUrl,
        fullUrl: file.fullUrl,
      })),
    }
  }
}
