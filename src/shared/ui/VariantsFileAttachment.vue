<script setup>
import FilePreview from '@shared/ui/FilePreview.vue'
import FormError from '@shared/ui/FormError.vue'
import { computed, ref } from 'vue'

const props = defineProps({
  questionScreenshot: {
    type: Object,
    required: true,
  },
  files: {
    type: Array,
    default: () => [],
  },
  screenshots: {
    type: Array,
    default: () => [],
  },
  error: {
    type: String,
    default: null,
  },
  errorKind: {
    type: String,
    default: null,
  },
  isScreenshotLoading: {
    type: Boolean,
    default: false,
  },
  shouldShowScreenshotButton: {
    type: Boolean,
    default: false,
  },
  buttonText: {
    type: String,
    default: null,
  },
  screenshotButtonText: {
    type: String,
    default: null,
  },
})

const emit = defineEmits(['fileUpload', 'screenshotRequest', 'remove'])

const fileInput = ref(null)

const allAttachments = computed(() => {
  const fileAttachments = props.files.map(file => ({
    ...file,
    type: file.type || 'file',
    previewUrl: file.previewUrl || '',
    fullUrl: file.fullUrl || '',
  }))

  const screenshotAttachments = props.screenshots.map(screenshot => ({
    ...screenshot,
    type: 'screenshot',
    name: `Screenshot ${new Date(screenshot.timestamp).toLocaleString()}`,
    previewUrl: '',
    fullUrl: '',
  }))

  return [...fileAttachments, ...screenshotAttachments]
})

const isMaxFilesReached = computed(() => {
  return allAttachments.value.length >= props.questionScreenshot.max_files
})

const shouldShowUploadButton = computed(() => {
  return props.questionScreenshot.upload_enabled === 1
})

function handleFileUpload() {
  if (!isMaxFilesReached.value) {
    fileInput.value?.click()
  }
}

function handleFileInputChange(event) {
  const files = Array.from(event.target.files)
  if (files.length > 0) {
    emit('fileUpload', files)
  }
  // Reset file input
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

function handleScreenshotRequest() {
  emit('screenshotRequest')
}

function handleRemove(index) {
  emit('remove', index)
}

function getFileType(attachment) {
  if (attachment.type === 'screenshot') {
    return 'image'
  }

  if (attachment.file && attachment.file.type) {
    if (attachment.file.type.startsWith('image/'))
      return 'image'
  }

  return attachment.type || 'file'
}
</script>

<template>
  <div class="file-attachment">
    <p v-if="questionScreenshot.description" class="file-attachment__description">
      {{ questionScreenshot.description }}
    </p>

    <div class="file-attachment__list">
      <TransitionGroup
        name="file-list" tag="div"
        class="file-attachment__items"
        :class="{ 'file-attachment__items--empty': allAttachments.length === 0,
                  'file-attachment__items--has-error': !!error,
        }"
      >
        <FilePreview
          v-for="(attachment, index) in allAttachments" :key="attachment.id || `${attachment.type}-${index}`"
          :file="attachment" :is-uploading="attachment.isUploading || false" :name="attachment.name"
          :type="attachment.type === 'screenshot' ? 'image' : getFileType(attachment)"
          :preview-url="attachment.previewUrl || ''" :full-url="attachment.fullUrl || ''"
          :open-in-new-tab="attachment.type === 'screenshot'" @remove="handleRemove(index)"
        />

        <FormError :key="errorKind || 'error'" :error="error" class="file-attachment__error" :class="{ 'file-attachment__error--too-large': errorKind === 'too-large' }" />
      </TransitionGroup>

      <div
        v-if="shouldShowUploadButton || shouldShowScreenshotButton"
        key="action-buttons"
        class="file-attachment__actions"
      >
        <button
          v-if="shouldShowUploadButton" :disabled="isMaxFilesReached" class="file-attachment__button" type="button"
          @click="handleFileUpload"
        >
          <div class="file-attachment__button-icon">
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                opacity="0.4"
                d="M12.5833 6.62071L7.22519 11.9714C6.56878 12.6269 5.67849 12.9951 4.75019 12.9951C3.82188 12.9951 2.93159 12.6269 2.27518 11.9714C1.61877 11.3159 1.25 10.4268 1.25 9.49982C1.25 8.57281 1.61877 7.68377 2.27518 7.02827L7.63332 1.67762C8.07093 1.24062 8.66445 0.995117 9.28333 0.995117C9.9022 0.995117 10.4957 1.24062 10.9333 1.67762C11.3709 2.11461 11.6168 2.70731 11.6168 3.32532C11.6168 3.94332 11.3709 4.53602 10.9333 4.97301L5.56936 10.3237C5.35055 10.5422 5.05379 10.6649 4.74436 10.6649C4.43492 10.6649 4.13816 10.5422 3.91935 10.3237C3.70055 10.1052 3.57763 9.80882 3.57763 9.49982C3.57763 9.19082 3.70055 8.89447 3.91935 8.67597L8.86937 3.7387"
                stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
              />
            </svg>
          </div>
          <span class="file-attachment__button-text">{{ buttonText || questionScreenshot.button_text }}</span>
        </button>

        <button
          v-if="shouldShowScreenshotButton" :disabled="isMaxFilesReached || isScreenshotLoading"
          class="file-attachment__button" type="button" @click="handleScreenshotRequest"
        >
          <div class="file-attachment__button-icon">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M2 4.5C2 3.67157 2.67157 3 3.5 3H4.5C4.77614 3 5 2.77614 5 2.5V2C5 1.44772 5.44772 1 6 1H10C10.5523 1 11 1.44772 11 2V2.5C11 2.77614 11.2239 3 11.5 3H12.5C13.3284 3 14 3.67157 14 4.5V12.5C14 13.3284 13.3284 14 12.5 14H3.5C2.67157 14 2 13.3284 2 12.5V4.5Z"
                stroke="currentColor" stroke-width="1.5"
              />
              <circle cx="8" cy="8.5" r="2.5" stroke="currentColor" stroke-width="1.5" />
            </svg>
          </div>
          <span class="file-attachment__button-text">
            {{ isScreenshotLoading ? 'Делаем скриншот...' : (screenshotButtonText || questionScreenshot.screenshot_button_text) }}
          </span>
        </button>
      </div>
    </div>

    <!-- Hidden file input -->
    <input
      ref="fileInput" type="file" multiple accept="image/*" style="display: none"
      @change="handleFileInputChange"
    >
  </div>
</template>

<style scoped>
.file-attachment {
  width: 100%;
}

.file-attachment__description {
  font-size: 16px;
  line-height: 1.3;
  color: var(--fqz-poll-text-on-place);
  margin: 0 0 15px 0;
}

.file-attachment__list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.file-attachment__items {
  display: flex;
  flex-wrap: wrap;
  gap: 15px 20px;
  transition: transform 0.3s;
}

.file-attachment__items--empty {
  display: none;
}

.file-attachment__items--empty.file-attachment__items--has-error {
  gap: 0;
  display: flex;
}

.file-attachment__actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

:deep(.file-attachment__error) {
  text-align: left;
  transition:
    margin-top 0.3s,
    transform 0.3s;
  width: 100%;
}

.file-attachment__button {
  all: unset;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.2);
  align-self: flex-start;
  padding: 0 15px;
  border-radius: var(--fqz-poll-next-button-radius, 18px);
  cursor: pointer;
  height: 36px;
  line-height: 1.3;
  color: #000;
  font-size: 12px;
  font-family: Roboto, sans-serif;
  transition: opacity 0.3s;
  gap: 10px;
  box-sizing: border-box;
}

.file-attachment__button:hover {
  opacity: 0.8;
}

.file-attachment__button:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed;
}

.file-attachment__button-icon {
  margin-top: -1px;
}

.file-attachment__button-icon svg {
  display: block;
}

.file-attachment__button-text {
  white-space: nowrap;
}

/* Transition animations */
.file-list-enter-active,
.file-list-leave-active,
.file-list-move {
  transition:
    transform 0.3s ease,
    opacity 0.3s ease;
}

.file-list-leave-active {
  position: absolute;
}

.file-list-leave-active.file-attachment__error {
  bottom: 0;
}

.file-attachment__error.file-list-leave-from {
  transform: translateY(0);
  opacity: 1;
}

.file-attachment__error.file-list-leave-to {
  opacity: 0;
  transform: translateY(4px);
}

.file-attachment__error--too-large.file-list-leave-to {
  transform: translateY(16px);
}

.file-list-enter-from,
.file-list-leave-to {
  opacity: 0;
  transform: translateY(4px);
}

@media (max-width: 679px) {
  .file-attachment__actions {
    flex-direction: column;
    align-items: stretch;
  }

  .file-attachment__button {
    width: 100%;
  }
}
</style>
