import { useMediaQuery } from '@vueuse/core'
import debounce from 'lodash.debounce'
import { defineStore } from 'pinia'
import { computed, nextTick, ref } from 'vue'
import { getScrollElement } from '../helpers/dom'

/**
 * Стора для работы с упрощенным режимом
 * Данный режим позволяет отображать опрос в упрощенном виде
 * Обычно используется для отображения опроса внутри iframe виджета
 */
export const useSimplifiedStore = defineStore('simplified', () => {
  const isSimplifiedMode = ref(false)
  const isInIframe = ref(false)
  const isWidgetMode = ref(false)
  const isMobile = useMediaQuery('(max-width: 679px)')
  const isHelloBoardWidget = ref(false)
  const closeByFinishButton = ref(false)
  const maxIframeHeight = ref<number | null>(null)
  const minIframeHeight = ref<number | null>(null)
  const isDialogOpen = ref(false)
  const openDialogsCount = ref(0)

  /**
   * Флаг для отображения виджета в режиме предпросмотра
   * Используется в сайдшите настроек виджета
   */
  const isPreviewMode = ref(false)

  const minDialogHeight = computed(() => isMobile.value ? 380 : 300)
  const isAutoHeight = ref(false)

  // Screenshot functionality
  const isScreenshotLoading = ref(false)
  const screenshotCallback = ref<((data: any) => void) | null>(null)

  const toggleSimplifiedMode = () => {
    isSimplifiedMode.value = !isSimplifiedMode.value
  }

  const setupIframeResizeObserver = () => {
    const scrollElement = getScrollElement()

    // Function to send height to parent
    const sendHeightToParent = () => {
      const bodyHeight = document.body.clientHeight
      window.parent.postMessage(
        JSON.stringify({
          type: 'fz:resize',
          height: bodyHeight,
        }),
        '*',
      )
    }

    const debouncedSendHeightToParent = debounce(sendHeightToParent, 100)

    // Set up ResizeObserver for the scroll element
    if (typeof ResizeObserver !== 'undefined') {
      const observer = new ResizeObserver(debouncedSendHeightToParent)

      const normalizedScrollElement = scrollElement instanceof Window ? document.body : scrollElement
      observer.observe(normalizedScrollElement)
    }
    else {
      // Also listen for window resize events
      window.addEventListener('resize', debouncedSendHeightToParent)
    }

    // Initial height send
    sendHeightToParent()
  }

  const initializeCloseByFinishButton = () => {
    if (!window?.location?.search) {
      return
    }

    const urlParams = new URLSearchParams(window.location.search)
    const closeByFinishParam = urlParams.get('close_by_finish_button')

    if (closeByFinishParam === '1') {
      closeByFinishButton.value = true
    }
  }

  const initialize = () => {
    const urlParams = new URLSearchParams(window.location.search)
    const simpleParam = urlParams.get('simple')
    const inFrameParam = urlParams.get('inFrame')
    const helloBoardWidgetParam = urlParams.get('helo-mode')
    const previewModeParam = urlParams.get('preview-mode')

    if (simpleParam === '1') {
      isSimplifiedMode.value = true
      document.body.classList.add('simplified-mode')
    }

    if (inFrameParam === '1') {
      isInIframe.value = true
      document.body.classList.add('simplified-iframe-mode')
    }

    if (helloBoardWidgetParam === '1') {
      isHelloBoardWidget.value = true
    }

    initializeCloseByFinishButton()

    if (previewModeParam === '1') {
      isPreviewMode.value = true
    }
  }

  /**
   * Initializes the simplified mode and iframe functionality
   */
  const initializeIframe = () => {
    if (isSimplifiedMode.value && isInIframe.value) {
      // Listen for dialog/popover events
      window.addEventListener('dialog:open', handleDialogOpen)
      window.addEventListener('dialog:close', handleDialogClose)

      // Listen for messages from parent window
      window.addEventListener('message', (event) => {
        try {
          const data = JSON.parse(event.data)

          if (data.type === 'fz:set_max_height') {
            maxIframeHeight.value = data.height

            const setAutoHeight = data.setAutoHeight
            const windowWidth = Number.parseInt(data.windowWidth)

            if (setAutoHeight || windowWidth < 679) {
              isAutoHeight.value = true
            }
            else {
              isAutoHeight.value = false
            }

            // Update CSS variable for max height
            document.documentElement.style.setProperty(
              '--fqz-app-max-iframe-height',
              `${data.height}px`,
            )

            if (data.minHeight) {
              minIframeHeight.value = data.minHeight
              document.documentElement.style.setProperty(
                '--fqz-app-min-iframe-height',
                `${data.minHeight}px`,
              )
            }
          }
          else if (data.type === 'SCREENSHOT_RESPONSE') {
            // Handle screenshot response from parent
            if (screenshotCallback.value) {
              screenshotCallback.value(data.data)
              screenshotCallback.value = null
            }
            isScreenshotLoading.value = false
          }
        }
        catch {
          // Ignore any errors from parsing
        }
      })

      // Set up resize handling
      nextTick().then(() => {
        setupIframeResizeObserver()
        sendAppReadyMessage()
      })
    }
  }

  const initializeWidgetMode = () => {
    const urlParams = new URLSearchParams(window.location.search)
    const previewModeParam = urlParams.get('preview-mode')

    // Параметр для отображения опроса в режиме виджета
    // @NOTE: Тут немного странная логика. Мы используем параметр `inFrame` для отображения опроса в режиме виджета
    // Данный параметр уже используется в `initializeIframe`, но initializeIframe вызывается только в режиме упрощенного опроса
    // Поэтому мы используем отдельный параметр для отображения опроса в режиме виджета
    const widgetModeParam = urlParams.get('inFrame')

    if (widgetModeParam === '1') {
      isWidgetMode.value = true
    }

    if (previewModeParam === '1') {
      isPreviewMode.value = true
    }
  }

  function sendAppReadyMessage() {
    window.parent.postMessage(
      JSON.stringify({
        type: 'fz:app_ready',
      }),
      '*',
    )
  }

  function closeWidget() {
    window.parent.postMessage(
      JSON.stringify({
        type: 'fz:close_widget',
      }),
      '*',
    )
  }

  function handleDialogOpen() {
    if (isSimplifiedMode.value && isInIframe.value) {
      openDialogsCount.value++
      isDialogOpen.value = true

      // Only adjust height for the first dialog
      if (openDialogsCount.value === 1) {
        const currentHeight = document.body.clientHeight
        if (currentHeight < minDialogHeight.value) {
          window.parent.postMessage(
            JSON.stringify({
              type: 'fz:resize',
              height: minDialogHeight.value,
            }),
            '*',
          )
        }
      }
    }
  }

  function handleDialogClose() {
    if (isSimplifiedMode.value && isInIframe.value) {
      openDialogsCount.value = Math.max(0, openDialogsCount.value - 1)
      isDialogOpen.value = openDialogsCount.value > 0

      // Only adjust height when closing the last dialog
      if (openDialogsCount.value === 0) {
        const currentHeight = document.body.clientHeight
        window.parent.postMessage(
          JSON.stringify({
            type: 'fz:resize',
            height: currentHeight,
          }),
          '*',
        )
      }
    }
  }

  /**
   * Request screenshot from parent window
   */
  function requestScreenshot(): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!isSimplifiedMode.value || !isInIframe.value) {
        reject(new Error('Screenshot only available in simplified iframe mode'))
        return
      }

      if (isScreenshotLoading.value) {
        reject(new Error('Screenshot already in progress'))
        return
      }

      isScreenshotLoading.value = true

      // Store callback for response
      screenshotCallback.value = resolve

      // Send request to parent
      window.parent.postMessage(
        JSON.stringify({
          type: 'REQUEST_SCREENSHOT',
        }),
        '*',
      )
    })
  }

  return {
    isSimplifiedMode,
    isInIframe,
    isHelloBoardWidget,
    maxIframeHeight,
    toggleSimplifiedMode,
    initialize,
    initializeIframe,
    initializeCloseByFinishButton,
    initializeWidgetMode,
    closeWidget,
    handleDialogOpen,
    handleDialogClose,
    isDialogOpen,
    openDialogsCount,
    isAutoHeight,
    closeByFinishButton,
    isWidgetMode,
    isPreviewMode,
    requestScreenshot,
    isScreenshotLoading,
  }
})
